package v1

import (
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"os"
	"path"
	"time"

	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core/config/app"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/response"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator/common"
	"github.com/avast/retry-go/v4"
	"github.com/shirou/gopsutil/v4/disk"
	"github.com/shirou/gopsutil/v4/mem"
)

// Sys 系统
// @Router /api/v1/sys []
type Sys struct {
}

// Resources 资源
// @Router /resources [get]
func (s *Sys) Resources() (*response.SysResourcesResponse, error) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Fatalln(err)
		return nil, common.ErrInternalServerError
	}
	wd, err := os.Getwd()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	// 监控项目根目录的磁盘使用情况
	projectDir := wd

	memoryStat, err := mem.VirtualMemory()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	memoryResult := response.SysResourcesResponseMemory{
		Total:       memoryStat.Total,
		Available:   memoryStat.Available,
		Used:        memoryStat.Used,
		UsedPercent: memoryStat.UsedPercent,
		Free:        memoryStat.Free,
	}

	projectDiskStat, err := disk.Usage(projectDir)
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}

	projectDiskResult := response.SysResourcesResponseDisk{
		Total:       projectDiskStat.Total,
		Free:        projectDiskStat.Free,
		Used:        projectDiskStat.Used,
		UsedPercent: projectDiskStat.UsedPercent,
	}

	return &response.SysResourcesResponse{
		Memory:      memoryResult,
		ProjectDisk: projectDiskResult,
	}, nil

}

// TransportChannelInfo
// @Router /transport-channel-info [get]
func (s *Sys) TransportChannelInfo() (any, error) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	cfg := core.GetConf().App.TransportChannel
	if cfg == nil {
		cfg = &app.TransportChannel{}
	}
	apiAddr := cfg.ApiAddr

	body, err := retry.DoWithData(func() ([]byte, error) {
		resp, err := http.Get(apiAddr)
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()
		return io.ReadAll(resp.Body)
	}, retry.Attempts(3))
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	return string(body), nil
}

type SftpServerStatus struct {
	Host     string `json:"host"`
	Port     uint16 `json:"port"`
	Username string `json:"username"`
	Status   string `json:"status"`
	Enabled  bool   `json:"enabled"`
}

type SftpAuthWithStatus struct {
	ResolveType string             `json:"resolve_type"`
	Servers     []SftpServerStatus `json:"servers"`
}

type SftpStatusResponse struct {
	Auth SftpAuthWithStatus `json:"auth"`
}

// SftpStatus
// @Router /sftp-status [get]
func (s *Sys) SftpStatus() (any, error) {
	var err error
	db, err := core.GetDatabase()
	if err != nil {
		return nil, common.ErrInternalServerError
	}

	var sysConfig model.SysConfig
	err = db.First(&sysConfig, model.SysConfigKey).Error
	if err != nil {
		return nil, common.ErrInternalServerError
	}

	if sysConfig.Sftp == nil {
		return SftpStatusResponse{}, nil
	}

	var sftpServers []SftpServerStatus
	if sysConfig.Sftp.Auth.ResolveType == "dns" {
		status := "Success"
		conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", sysConfig.Sftp.Auth.Host, sysConfig.Sftp.Auth.Port), time.Second*3)
		if err != nil {
			status = "Error"
		} else {
			conn.Close()
		}
		sftpServers = append(sftpServers, SftpServerStatus{
			Host:     sysConfig.Sftp.Auth.Host,
			Port:     sysConfig.Sftp.Auth.Port,
			Username: sysConfig.Sftp.Auth.Username,
			Status:   status,
			Enabled:  true,
		})
	} else {
		for _, ipConfig := range sysConfig.Sftp.Auth.IpConfigs {
			status := "Success"
			conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", ipConfig.Host, ipConfig.Port), time.Second*3)
			if err != nil {
				status = "Error"
			} else {
				conn.Close()
			}

			sftpServers = append(sftpServers, SftpServerStatus{
				Host:     ipConfig.Host,
				Port:     ipConfig.Port,
				Username: ipConfig.Username,
				Status:   status,
				Enabled:  ipConfig.Active,
			})
		}
	}

	return SftpStatusResponse{
		Auth: SftpAuthWithStatus{
			ResolveType: sysConfig.Sftp.Auth.ResolveType,
			Servers:     sftpServers,
		},
	}, nil
}

type DicomServerStatus struct {
	Host      string `json:"host"`
	Port      int16  `json:"port"`
	ServerAet string `json:"server_aet"`
	Status    string `json:"status"`
	Enabled   bool   `json:"enabled"`
}

type DicomAuthWithStatus struct {
	ResolveType string              `json:"resolve_type"`
	Servers     []DicomServerStatus `json:"servers"`
}

type DicomStatusResponse struct {
	Auth DicomAuthWithStatus `json:"auth"`
}

// DicomStatus
// @Router /dicom-status [get]
func (s *Sys) DicomStatus() (any, error) {
	var err error
	db, err := core.GetDatabase()
	if err != nil {
		return nil, common.ErrInternalServerError
	}

	var sysConfig model.SysConfig
	err = db.First(&sysConfig, model.SysConfigKey).Error
	if err != nil {
		return nil, common.ErrInternalServerError
	}

	if sysConfig.Dicom == nil {
		return DicomStatusResponse{}, nil
	}

	var dicomServers []DicomServerStatus
	if sysConfig.Dicom.Auth.ResolveType == "dns" {
		status := "Success"
		conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", sysConfig.Dicom.Auth.ServerIp, sysConfig.Dicom.Auth.ServerPort), time.Second*3)
		if err != nil {
			status = "Error"
		} else {
			conn.Close()
		}
		dicomServers = append(dicomServers, DicomServerStatus{
			Host:      sysConfig.Dicom.Auth.ServerIp,
			Port:      int16(sysConfig.Dicom.Auth.ServerPort),
			ServerAet: sysConfig.Dicom.Auth.ServerAet,
			Status:    status,
			Enabled:   true,
		})
	} else {
		for _, ipConfig := range sysConfig.Dicom.Auth.IpConfigs {
			status := "Success"
			conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", ipConfig.ServerIp, ipConfig.ServerPort), time.Second*3)
			if err != nil {
				status = "Error"
			} else {
				conn.Close()
			}

			dicomServers = append(dicomServers, DicomServerStatus{
				Host:      ipConfig.ServerIp,
				Port:      ipConfig.ServerPort,
				ServerAet: ipConfig.ServerAet,
				Status:    status,
				Enabled:   ipConfig.Active,
			})
		}
	}

	return DicomStatusResponse{
		Auth: DicomAuthWithStatus{
			ResolveType: sysConfig.Dicom.Auth.ResolveType,
			Servers:     dicomServers,
		},
	}, nil
}
