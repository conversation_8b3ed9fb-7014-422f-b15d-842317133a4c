# 全局错误日志

# 主进程 PID

# 工作进程
worker_processes auto;

events {
    worker_connections 768;
}

http {
    ## 所有临时文件路径都指向 /tmp
    client_body_temp_path /tmp/client_body_temp;
    proxy_temp_path       /tmp/proxy_temp;
    fastcgi_temp_path     /tmp/fastcgi_temp;
    uwsgi_temp_path       /tmp/uwsgi_temp;
    scgi_temp_path        /tmp/scgi_temp;

    ## 日志路径
    access_log /tmp/nginx_access.log;
    error_log  /tmp/nginx_error.log;

    ## 基础设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    ## Gzip
    gzip on;

    ## 服务器配置
    server {
        listen 80 default_server;
        listen [::]:80 default_server;

        root /app/web;
        index index.html index.htm index.nginx-debian.html;

        server_name _;

        client_max_body_size 200M;

        location ^~/api/ {
            proxy_pass http://localhost:8999;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            proxy_connect_timeout 15s;
            proxy_read_timeout 60s;
            proxy_send_timeout 60s;
        }

        location / {
            try_files $uri $uri/ /index.html;
        }
    }
}