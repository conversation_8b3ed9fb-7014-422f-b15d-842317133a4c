services:
  mysql:
    container_name: hk_box_mysql
    image: registry.datasecchk.net/library/mysql:8.0.39
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=ChangeMe
    volumes:
      - ./mysql/datadir:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
      - ./mysql/initdb.d/:/docker-entrypoint-initdb.d
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
    networks:
      cnix:
        ipv4_address: ***********

  redis:
    container_name: hk_box_redis
    image: registry.datasecchk.net/library/redis:7.4.0
    restart: always
    volumes:
      - ./redis/conf:/usr/local/etc/redis
      - ./redis/data:/data
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
    networks:
      cnix:
        ipv4_address: ***********

  # caddy:
  #   container_name: caddy
  #   image: registry.datasecchk.net/library/caddy:2.10.2
  #   restart: always
  #   ports:
  #     - "443:443"
  #   volumes:
  #     - ./caddy/Caddyfile:/etc/caddy/Caddyfile
  #   logging:
  #     driver: "json-file"
  #     options:
  #       max-size: "10m"
  #       max-file: "10"
  #   networks:
  #     cnix:
  #       ipv4_address: ***********

networks:
  cnix:
    external: true
